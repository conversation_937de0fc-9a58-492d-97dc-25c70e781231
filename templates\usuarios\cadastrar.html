{% extends "base.html" %}

{% block title %}Novo Usuário - Sistema de Controle de Dossiê Escolar{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{{ url_for('usuario.listar') }}">Usuários</a></li>
<li class="breadcrumb-item active">Novo Usuário</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user-plus me-2"></i>
                Novo Usuário
            </h1>
            <a href="{{ url_for('usuario.listar') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Voltar
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    Dados do Usuário
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="formUsuario">
                    <!-- Foto do Usuário -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">
                                <i class="fas fa-camera me-2"></i>
                                Foto do Usuário
                            </h6>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="text-center">
                                <img id="previewFoto"
                                     src="/static/img/default-avatar.svg"
                                     alt="Preview da foto"
                                     class="rounded-circle img-thumbnail mb-3"
                                     style="width: 150px; height: 150px; object-fit: cover; cursor: pointer;"
                                     onclick="document.getElementById('foto').click()">

                                <div>
                                    <input type="file"
                                           id="foto"
                                           name="foto"
                                           accept="image/*"
                                           style="display: none;"
                                           onchange="previewImage(this)">

                                    <button type="button"
                                            class="btn btn-outline-primary btn-sm me-2"
                                            onclick="document.getElementById('foto').click()">
                                        <i class="fas fa-camera me-1"></i>Selecionar Foto
                                    </button>

                                    <button type="button"
                                            class="btn btn-outline-secondary btn-sm"
                                            onclick="removePreview()"
                                            id="btnRemoverFoto"
                                            style="display: none;">
                                        <i class="fas fa-trash me-1"></i>Remover
                                    </button>
                                </div>

                                <small class="text-muted d-block mt-2">
                                    Formatos: PNG, JPG, JPEG, GIF<br>
                                    Tamanho máximo: 5MB
                                </small>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <!-- Dados Pessoais -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2">
                                        <i class="fas fa-id-card me-2"></i>
                                        Dados Pessoais
                                    </h6>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label for="nome" class="form-label">Nome Completo *</label>
                                    <input type="text" class="form-control" id="nome" name="nome" required
                                           placeholder="Nome completo do usuário">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="cpf" class="form-label">CPF</label>
                                    <input type="text" class="form-control" id="cpf" name="cpf"
                                           placeholder="000.000.000-00">
                                </div>
                                <div class="col-md-6">
                                    <label for="data_nascimento" class="form-label">Data de Nascimento</label>
                                    <input type="date" class="form-control" id="data_nascimento" name="data_nascimento">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Dados de Contato -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">
                                <i class="fas fa-envelope me-2"></i>
                                Dados de Contato
                            </h6>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="email" class="form-label">Email *</label>
                            <input type="email" class="form-control" id="email" name="email" required
                                   placeholder="<EMAIL>">
                        </div>
                        <div class="col-md-6">
                            <label for="telefone" class="form-label">Telefone</label>
                            <input type="text" class="form-control" id="telefone" name="telefone"
                                   placeholder="(11) 99999-9999">
                        </div>
                    </div>
                    
                    <!-- Dados do Sistema -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">
                                <i class="fas fa-cogs me-2"></i>
                                Configurações do Sistema
                            </h6>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="escola_id" class="form-label">Escola *</label>
                            <select class="form-select" id="escola_id" name="escola_id" required>
                                <option value="">Selecione a escola...</option>
                                {% for escola in escolas %}
                                <option value="{{ escola.id }}">{{ escola.nome }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="perfil_id" class="form-label">Perfil de Acesso *</label>
                            <select class="form-select" id="perfil_id" name="perfil_id" required>
                                <option value="">Selecione o perfil...</option>
                                {% for perfil in perfis %}
                                <option value="{{ perfil.id }}">{{ perfil.nome }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="situacao" class="form-label">Situação</label>
                            <select class="form-select" id="situacao" name="situacao">
                                <option value="ativo" selected>Ativo</option>
                                <option value="inativo">Inativo</option>
                                <option value="bloqueado">Bloqueado</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Senha -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">
                                <i class="fas fa-key me-2"></i>
                                Senha de Acesso
                            </h6>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="senha" class="form-label">Senha Inicial</label>
                            <input type="password" class="form-control" id="senha" name="senha" 
                                   value="123456" placeholder="Senha padrão: 123456">
                            <div class="form-text">Deixe em branco para usar senha padrão: 123456</div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Importante:</strong> O usuário deverá alterar a senha no primeiro acesso.
                            </div>
                        </div>
                    </div>
                    
                    <!-- Botões -->
                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ url_for('usuario.listar') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    Cancelar
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-check me-2"></i>
                                    Cadastrar Usuário
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Máscara para CPF
    $('#cpf').mask('000.000.000-00');
    
    // Máscara para telefone
    $('#telefone').mask('(00) 00000-0000');
    
    // Validação do formulário
    $('#formUsuario').on('submit', function(e) {
        var nome = $('#nome').val().trim();
        var email = $('#email').val().trim();
        var escola_id = $('#escola_id').val();
        var perfil_id = $('#perfil_id').val();
        
        if (!nome || !email || !escola_id || !perfil_id) {
            e.preventDefault();
            alert('Por favor, preencha todos os campos obrigatórios (*)');
            return false;
        }
        
        // Validar email
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            alert('Por favor, insira um email válido');
            $('#email').focus();
            return false;
        }
        
        // Confirmar cadastro
        if (!confirm('Confirma o cadastro do usuário "' + nome + '"?')) {
            e.preventDefault();
            return false;
        }
    });
    
    // Auto-gerar email baseado no nome
    $('#nome').on('blur', function() {
        var nome = $(this).val().trim();
        if (nome && !$('#email').val()) {
            var email = nome.toLowerCase()
                .normalize('NFD')
                .replace(/[\u0300-\u036f]/g, '')
                .replace(/[^a-z\s]/g, '')
                .replace(/\s+/g, '.')
                .substring(0, 30) + '@escola.gov.br';
            $('#email').val(email);
        }
    });
});

// Funções para preview da foto
function previewImage(input) {
    if (input.files && input.files[0]) {
        // Validar tamanho do arquivo (5MB)
        if (input.files[0].size > 5 * 1024 * 1024) {
            alert('Arquivo muito grande! O tamanho máximo é 5MB.');
            input.value = '';
            return;
        }

        // Validar tipo do arquivo
        const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(input.files[0].type)) {
            alert('Tipo de arquivo não permitido! Use PNG, JPG, JPEG, GIF ou WEBP.');
            input.value = '';
            return;
        }

        const reader = new FileReader();

        reader.onload = function(e) {
            document.getElementById('previewFoto').src = e.target.result;
            document.getElementById('btnRemoverFoto').style.display = 'inline-block';
        }

        reader.readAsDataURL(input.files[0]);
    }
}

function removePreview() {
    document.getElementById('previewFoto').src = '/static/img/default-avatar.svg';
    document.getElementById('foto').value = '';
    document.getElementById('btnRemoverFoto').style.display = 'none';
}
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.16/jquery.mask.min.js"></script>
{% endblock %}
