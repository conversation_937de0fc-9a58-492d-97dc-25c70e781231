<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ usuario.nome }} - Sistema Modular</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-graduation-cap me-2"></i>Sistema Modular
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('auth.logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1>
                            <i class="fas fa-user me-2"></i>
                            {{ usuario.nome }}
                            <span class="badge bg-{{ 'danger' if usuario.perfil_obj.nome == 'Administrador Geral' else 'warning' if 'Administrador' in usuario.perfil_obj.nome else 'info' }}">
                                {{ usuario.perfil_obj.nome }}
                            </span>
                        </h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="{{ url_for('usuario.listar') }}">Usuários</a></li>
                                <li class="breadcrumb-item active">{{ usuario.nome }}</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="btn-group">
                        <a href="{{ url_for('usuario.editar', id=usuario.id) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>Editar
                        </a>
                        <a href="{{ url_for('usuario.listar') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Voltar
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Informações Pessoais -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            Informações Pessoais
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Nome:</strong></td>
                                        <td>{{ usuario.nome }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>CPF:</strong></td>
                                        <td>
                                            {% if usuario.cpf %}
                                                <code>{{ usuario.cpf }}</code>
                                            {% else %}
                                                <span class="text-muted">Não informado</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>
                                            <a href="mailto:{{ usuario.email }}">{{ usuario.email }}</a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Telefone:</strong></td>
                                        <td>
                                            {% if usuario.telefone %}
                                                {{ usuario.telefone }}
                                            {% else %}
                                                <span class="text-muted">Não informado</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Cargo:</strong></td>
                                        <td>
                                            {% if usuario.cargo %}
                                                {{ usuario.cargo }}
                                            {% else %}
                                                <span class="text-muted">Não informado</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Perfil:</strong></td>
                                        <td>
                                            <span class="badge bg-{{ 'danger' if usuario.perfil_obj.nome == 'Administrador Geral' else 'warning' if 'Administrador' in usuario.perfil_obj.nome else 'info' }}">
                                                {{ usuario.perfil_obj.nome }}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Escola:</strong></td>
                                        <td>{{ usuario.escola.nome }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if usuario.status == 'ativo' else 'danger' if usuario.status == 'inativo' else 'warning' }}">
                                                {{ usuario.status.title() }}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Data Cadastro:</strong></td>
                                        <td>{{ usuario.data_cadastro.strftime('%d/%m/%Y às %H:%M') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Último Acesso:</strong></td>
                                        <td>
                                            {% if usuario.ultimo_acesso %}
                                                {{ usuario.ultimo_acesso.strftime('%d/%m/%Y às %H:%M') }}
                                            {% else %}
                                                <span class="text-muted">Nunca acessou</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        {% if usuario.endereco %}
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6><strong>Endereço:</strong></h6>
                                <p class="text-muted">{{ usuario.endereco }}</p>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Estatísticas e Ações -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            Estatísticas
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h4>{{ usuario.total_dossies_criados or 0 }}</h4>
                                        <small>Dossiês Criados</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="card bg-warning text-dark">
                                    <div class="card-body">
                                        <h4>{{ usuario.total_movimentacoes or 0 }}</h4>
                                        <small>Movimentações</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h4>{{ usuario.total_logins or 0 }}</h4>
                                        <small>Total Logins</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body">
                                        <h4>{{ usuario.dias_desde_ultimo_acesso or 'N/A' }}</h4>
                                        <small>Dias sem Acesso</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ações Rápidas -->
                <div class="card mt-3">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            Ações Rápidas
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('dossie.listar') }}?usuario={{ usuario.id }}" class="btn btn-outline-primary">
                                <i class="fas fa-folder me-2"></i>Ver Dossiês Criados
                            </a>
                            <a href="{{ url_for('movimentacao.listar') }}?usuario={{ usuario.id }}" class="btn btn-outline-warning">
                                <i class="fas fa-exchange-alt me-2"></i>Ver Movimentações
                            </a>
                            <button type="button" class="btn btn-outline-info" onclick="alert('Funcionalidade em desenvolvimento')">
                                <i class="fas fa-history me-2"></i>Histórico de Acessos
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="resetarSenha({{ usuario.id }})">
                                <i class="fas fa-key me-2"></i>Resetar Senha
                            </button>
                            <hr>
                            <button type="button" class="btn btn-outline-danger" onclick="bloquearUsuario({{ usuario.id }}, '{{ usuario.nome }}')">
                                <i class="fas fa-ban me-2"></i>{{ 'Desbloquear' if usuario.situacao == 'bloqueado' else 'Bloquear' }} Usuário
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Permissões -->
                <div class="card mt-3">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            Permissões
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                <h6>Nível de Acesso: {{ usuario.perfil_obj.nivel_acesso }}</h6>
                                <p class="text-muted small">{{ usuario.perfil_obj.descricao }}</p>
                                
                                <h6>Pode acessar:</h6>
                                <ul class="list-unstyled small">
                                    {% if usuario.perfil_obj.nome == 'Administrador Geral' %}
                                        <li><i class="fas fa-check text-success me-2"></i>Todas as escolas</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Todos os usuários</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Todos os dossiês</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Relatórios gerais</li>
                                    {% elif 'Administrador' in usuario.perfil_obj.nome %}
                                        <li><i class="fas fa-check text-success me-2"></i>Sua escola: {{ usuario.escola.nome }}</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Usuários da escola</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Dossiês da escola</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Relatórios da escola</li>
                                    {% else %}
                                        <li><i class="fas fa-check text-success me-2"></i>Consultar dossiês</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Criar movimentações</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Seu perfil</li>
                                    {% endif %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function resetarSenha(userId) {
            if (confirm('Confirma o reset da senha do usuário? A nova senha será "123456".')) {
                // Implementar chamada AJAX para resetar senha
                fetch(`/usuarios/resetar-senha/${userId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Senha resetada com sucesso! Nova senha: 123456');
                    } else {
                        alert('Erro ao resetar senha: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('Funcionalidade em desenvolvimento. Será implementada em breve.');
                });
            }
        }

        function bloquearUsuario(userId, userName) {
            const acao = '{{ usuario.situacao }}' === 'bloqueado' ? 'desbloquear' : 'bloquear';

            if (confirm(`Confirma ${acao} o usuário "${userName}"?`)) {
                // Implementar chamada AJAX para bloquear/desbloquear
                fetch(`/usuarios/${acao}/${userId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`Usuário ${acao}ado com sucesso!`);
                        location.reload();
                    } else {
                        alert(`Erro ao ${acao} usuário: ` + data.message);
                    }
                })
                .catch(error => {
                    alert('Funcionalidade em desenvolvimento. Será implementada em breve.');
                });
            }
        }
    </script>
</body>
</html>
