{% extends "base.html" %}

{% block title %}Editar {{ usuario.nome }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user-edit me-2"></i>
                Editar Usuário
            </h1>
            <div>
                <a href="{{ url_for('usuario.ver', id=usuario.id) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Voltar
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-user-edit me-2"></i>
                    Editando: {{ usuario.nome }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-8">
                            <label for="nome" class="form-label">Nome Completo *</label>
                            <input type="text" class="form-control" id="nome" name="nome" value="{{ usuario.nome }}" required>
                        </div>
                        <div class="col-md-4">
                            <label for="cpf" class="form-label">CPF</label>
                            <input type="text" class="form-control" id="cpf" name="cpf" value="{{ usuario.cpf or '' }}" placeholder="000.000.000-00">
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="email" class="form-label">Email *</label>
                            <input type="email" class="form-control" id="email" name="email" value="{{ usuario.email }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="telefone" class="form-label">Telefone</label>
                            <input type="text" class="form-control" id="telefone" name="telefone" value="{{ usuario.telefone or '' }}" placeholder="(00) 00000-0000">
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-4">
                            <label for="data_nascimento" class="form-label">Data de Nascimento</label>
                            <input type="date" class="form-control" id="data_nascimento" name="data_nascimento" 
                                   value="{{ usuario.data_nascimento.strftime('%Y-%m-%d') if usuario.data_nascimento else '' }}">
                        </div>
                        <div class="col-md-4">
                            <label for="perfil_id" class="form-label">Perfil *</label>
                            <select class="form-select" id="perfil_id" name="perfil_id" required>
                                <option value="">Selecione...</option>
                                {% for perfil in perfis %}
                                    <option value="{{ perfil.id }}" {{ 'selected' if usuario.perfil_id == perfil.id else '' }}>
                                        {{ perfil.nome }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="situacao" class="form-label">Situação</label>
                            <select class="form-select" id="situacao" name="situacao">
                                <option value="ativo" {{ 'selected' if usuario.situacao == 'ativo' else '' }}>Ativo</option>
                                <option value="inativo" {{ 'selected' if usuario.situacao == 'inativo' else '' }}>Inativo</option>
                                <option value="bloqueado" {{ 'selected' if usuario.situacao == 'bloqueado' else '' }}>Bloqueado</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="escola_id" class="form-label">Escola *</label>
                            <select class="form-select" id="escola_id" name="escola_id" required>
                                <option value="">Selecione...</option>
                                {% for escola in escolas %}
                                    <option value="{{ escola.id }}" {{ 'selected' if usuario.escola_id == escola.id else '' }}>
                                        {{ escola.nome }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="nova_senha" class="form-label">Nova Senha</label>
                            <input type="password" class="form-control" id="nova_senha" name="nova_senha" placeholder="Deixe em branco para manter a atual">
                            <div class="form-text">Deixe em branco se não quiser alterar a senha</div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Informações importantes:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>O email é usado como login do usuário</li>
                                    <li>Alterações no perfil afetam as permissões do usuário</li>
                                    <li>Se alterar a senha, o usuário precisará usar a nova senha no próximo login</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ url_for('usuario.ver', id=usuario.id) }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Cancelar
                                </a>
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save me-2"></i>Salvar Alterações
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Informações Adicionais -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Informações do Sistema
                </h6>
            </div>
            <div class="card-body">
                <p><strong>Cadastrado em:</strong> {{ usuario.data_cadastro.strftime('%d/%m/%Y às %H:%M') if usuario.data_cadastro else 'Não informado' }}</p>
                <p><strong>Último acesso:</strong> {{ usuario.ultimo_acesso.strftime('%d/%m/%Y às %H:%M') if usuario.ultimo_acesso else 'Nunca acessou' }}</p>
                <p><strong>Tentativas de login:</strong> {{ usuario.tentativas_login or 0 }}</p>
                <p><strong>Status:</strong> 
                    <span class="badge bg-{{ 'success' if usuario.situacao == 'ativo' else 'danger' if usuario.situacao == 'bloqueado' else 'warning' }}">
                        {{ usuario.situacao.title() }}
                    </span>
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    Permissões
                </h6>
            </div>
            <div class="card-body">
                <p><strong>Perfil atual:</strong> {{ usuario.perfil_obj.nome }}</p>
                <p><strong>Descrição:</strong> {{ usuario.perfil_obj.descricao }}</p>
                <p><strong>Escola:</strong> {{ usuario.escola.nome }}</p>
                {% if usuario.is_admin_geral() %}
                    <div class="alert alert-warning alert-sm">
                        <i class="fas fa-crown me-2"></i>
                        <strong>Administrador Geral</strong> - Acesso total ao sistema
                    </div>
                {% elif usuario.is_admin_escola() %}
                    <div class="alert alert-info alert-sm">
                        <i class="fas fa-user-shield me-2"></i>
                        <strong>Administrador da Escola</strong> - Acesso administrativo à escola
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Máscara para CPF
    document.getElementById('cpf').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        value = value.replace(/(\d{3})(\d)/, '$1.$2');
        value = value.replace(/(\d{3})(\d)/, '$1.$2');
        value = value.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
        e.target.value = value;
    });

    // Máscara para telefone
    document.getElementById('telefone').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length <= 10) {
            value = value.replace(/^(\d{2})(\d)/, '($1) $2');
            value = value.replace(/(\d{4})(\d)/, '$1-$2');
        } else {
            value = value.replace(/^(\d{2})(\d)/, '($1) $2');
            value = value.replace(/(\d{5})(\d)/, '$1-$2');
        }
        e.target.value = value;
    });

    // Validação de email
    document.getElementById('email').addEventListener('blur', function(e) {
        const email = e.target.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            e.target.classList.add('is-invalid');
            if (!document.querySelector('.invalid-feedback')) {
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = 'Por favor, insira um email válido';
                e.target.parentNode.appendChild(feedback);
            }
        } else {
            e.target.classList.remove('is-invalid');
            const feedback = e.target.parentNode.querySelector('.invalid-feedback');
            if (feedback) {
                feedback.remove();
            }
        }
    });

    // Confirmação antes de salvar
    document.querySelector('form').addEventListener('submit', function(e) {
        if (!confirm('Confirma as alterações no usuário?')) {
            e.preventDefault();
        }
    });
</script>
{% endblock %}
